#app-logo {
  max-width: 150px;
  width: 100%;
  height: 100%;
  object-fit: "cover";
}
#landing-page-content {
  display: grid;
  gap: 2rem;
}
section:not(#home) {
  scroll-margin-top: 80px;
}
section {
  max-width: 1200px;
  min-height: 50vh;
  padding: 1.2rem;
  width: 100%;
  margin: auto;
}
.toolbar-content-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 1rem;
  margin: auto;
}
@media screen and (min-width: 600px) {
  .toolbar-content-wrapper {
    max-width: 80vw;
  }
}
#menu {
  display: flex;
  justify-content: space-evenly;
  padding: 0 1.2rem;
}
#menu a {
  text-decoration: none;
}
#menu a:hover {
  color: #2d90e0;
}
#menu.mobile-menu {
  display: none;
  padding: 0;
}
.mobile-menu {
  position: absolute;
  gap: 1rem;
  top: 56px;
  right: 0;
  left: 0;
  background-color: white;
  padding-bottom: 1rem;
}
#menu.mobile-menu.active {
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2);
}
#menu.mobile-menu.active a {
  padding: 1rem 1.2rem !important;
}
#app-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.feature-card {
  width: 300px;
  display: "flex";
  flex-direction: "column";
  height: "100%";
}
.feature-card h5 {
  font-weight: 700;
}
#landing-page-content .MuiCard-root {
  transition: all 0.1s ease-out;
}
#landing-page-content .MuiCard-root:hover {
  transition: all 0.1s ease-in-out;
  transform: scale(1.1) !important;
}
#app-preview-container {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  position: relative;
  overflow: hidden;
  min-height: 50vh;
  height: 100%;
  max-width: 750px;
  min-width: 350px;
  margin: auto;
}
#app-preview-container .grid-item {
  grid-row: 1;
  grid-column: 1;
  display: flex;
  place-content: center;
  place-items: center;
  overflow: visible;
  position: relative;
}
#app-preview-container .grid-item:first {
  z-index: 2;
}
#app-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
#main-app-preview {
  width: 90%;
  height: 90%;
  object-fit: contain;
}
#main-app-preview-hover {
  margin-left: 1.8rem;
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0%;
  transition: opacity 0.3s ease-in-out;
}
#home:hover #main-app-preview-hover {
  opacity: 100%;
}
@media screen and (max-width: 600px) {
  #app-preview-container {
    height: 100%;
  }
  #app-preview-container * {
    position: relative !important;
    height: 100%;
  }
}
@media (pointer: none), (pointer: coarse) {
  #main-app-preview-hover {
    opacity: 100%;
  }
}

html {
  scroll-behavior: smooth;
}