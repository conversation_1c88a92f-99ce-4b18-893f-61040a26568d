import { DataTypes } from "sequelize";
import sequelize from "../database.js";

const RoutineExercise = sequelize.define(
  "RoutineExercise",
  {
    id: {
      type: DataTypes.UUID,
      allowNull: false,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
    },
    routine_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    exercise_id: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    sets: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    reps: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    rest_time: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 30000,
    },
  },
  {
    tableName: "routine_exercise",
    timestamps: false,
  }
);

export default RoutineExercise;
