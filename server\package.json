{"name": "server", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server", "dev": "nodemon server", "sync-db": "node syncDatabase.js", "sync-db-force": "node syncDatabase.js --force"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.7", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "express": "^4.21.0", "groq-sdk": "^0.8.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "openai": "^4.67.3", "pg": "^8.13.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.4", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.1.2", "uuid": "^11.0.3", "wkx": "^0.5.0"}, "devDependencies": {"nodemon": "^3.1.4"}}