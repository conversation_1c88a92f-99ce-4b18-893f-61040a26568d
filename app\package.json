{"name": "bodybuddy", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mediapipe/tasks-vision": "^0.10.16", "@mui/icons-material": "^6.1.1", "@mui/material": "^6.1.5", "@mui/x-data-grid": "^7.18.0", "@mui/x-date-pickers": "^7.22.0", "@supabase/supabase-js": "^2.45.4", "@toolpad/core": "^0.7.0", "axios": "^1.7.7", "chart.js": "^4.4.4", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "emailjs-com": "^3.2.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-countdown-circle-timer": "^3.2.1", "react-dom": "^18.3.1", "react-player": "^2.16.0", "react-router-dom": "^6.26.2", "react-webcam": "^7.2.0", "uuid": "^11.0.3"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.1"}}